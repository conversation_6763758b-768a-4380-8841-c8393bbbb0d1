#!/usr/bin/env python3
"""
Amazon商品评论获取工具
支持多种方案：API方式（推荐）和浏览器自动化方式
"""

import requests
import time
import json
import csv
from datetime import datetime
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class Review:
    """评论数据结构"""
    reviewer_name: str
    rating: float
    title: str
    content: str
    date: str
    verified_purchase: bool
    helpful_votes: int = 0
    
class AmazonReviewScraper:
    """Amazon评论获取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def method_1_rainforest_api(self, asin: str, api_key: str) -> List[Review]:
        """
        方案1: 使用Rainforest API (推荐)
        - 合法合规
        - 稳定可靠
        - 需要付费但价格合理
        """
        logger.info("使用Rainforest API获取评论...")

        url = "https://api.rainforestapi.com/request"
        params = {
            'api_key': api_key,
            'type': 'reviews',
            'amazon_domain': 'amazon.nl',
            'asin': asin,
            'review_stars': 'all_stars',
            'sort_by': 'recent'
        }

        reviews = []
        page = 1
        max_retries = 3

        while page <= 10:  # 限制最多10页，避免过多请求
            params['page'] = page

            for retry in range(max_retries):
                try:
                    logger.info(f"正在获取第{page}页评论... (尝试 {retry + 1}/{max_retries})")
                    response = self.session.get(url, params=params, timeout=30)

                    # 检查响应状态
                    if response.status_code == 503:
                        logger.warning(f"服务暂时不可用 (503)，等待 {(retry + 1) * 5} 秒后重试...")
                        time.sleep((retry + 1) * 5)
                        continue
                    elif response.status_code == 429:
                        logger.warning(f"请求过于频繁 (429)，等待 {(retry + 1) * 10} 秒后重试...")
                        time.sleep((retry + 1) * 10)
                        continue
                    elif response.status_code == 401:
                        logger.error("API密钥无效 (401)，请检查密钥是否正确")
                        return reviews

                    response.raise_for_status()
                    data = response.json()

                    # 检查API响应格式
                    if 'error' in data:
                        logger.error(f"API返回错误: {data['error']}")
                        return reviews

                    if 'reviews' not in data:
                        logger.info(f"第{page}页没有更多评论")
                        return reviews

                    page_reviews = data['reviews']
                    if not page_reviews:
                        logger.info(f"第{page}页评论为空，结束获取")
                        return reviews

                    for review_data in page_reviews:
                        try:
                            review = Review(
                                reviewer_name=review_data.get('profile', {}).get('name', '') if review_data.get('profile') else '',
                                rating=float(review_data.get('rating', 0)),
                                title=review_data.get('title', ''),
                                content=review_data.get('body', ''),
                                date=review_data.get('date', ''),
                                verified_purchase=review_data.get('verified_purchase', False),
                                helpful_votes=review_data.get('helpful_votes', 0)
                            )
                            reviews.append(review)
                        except Exception as e:
                            logger.warning(f"解析单条评论失败: {e}")
                            continue

                    logger.info(f"成功获取第{page}页评论，共{len(page_reviews)}条")
                    page += 1
                    time.sleep(2)  # 增加延迟避免请求过快
                    break  # 成功获取，跳出重试循环

                except requests.exceptions.Timeout:
                    logger.warning(f"请求超时，等待 {(retry + 1) * 3} 秒后重试...")
                    time.sleep((retry + 1) * 3)
                except requests.exceptions.ConnectionError:
                    logger.warning(f"连接错误，等待 {(retry + 1) * 3} 秒后重试...")
                    time.sleep((retry + 1) * 3)
                except Exception as e:
                    logger.error(f"获取第{page}页评论失败: {e}")
                    if retry == max_retries - 1:
                        logger.error(f"第{page}页重试{max_retries}次后仍然失败，跳过此页")
                        page += 1
                        break
                    time.sleep((retry + 1) * 2)

        return reviews
    
    def method_2_scrapfly_api(self, asin: str, api_key: str) -> List[Review]:
        """
        方案2: 使用ScrapFly API
        - 专业的网页抓取服务
        - 自动处理反爬虫
        """
        logger.info("使用ScrapFly API获取评论...")
        
        url = "https://api.scrapfly.io/scrape"
        
        # 构建Amazon评论页面URL
        review_url = f"https://www.amazon.nl/product-reviews/{asin}/ref=cm_cr_arp_d_viewopt_sr"
        
        params = {
            'key': api_key,
            'url': review_url,
            'render_js': True,
            'country': 'NL',
            'asp': True  # 反检测
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            # 这里需要解析HTML内容提取评论
            # 具体实现需要根据ScrapFly返回的数据格式调整
            html_content = data.get('result', {}).get('content', '')
            reviews = self._parse_html_reviews(html_content)
            
            return reviews
            
        except Exception as e:
            logger.error(f"ScrapFly API请求失败: {e}")
            return []
    
    def method_3_selenium_browser(self, product_url: str) -> List[Review]:
        """
        方案3: 使用Selenium浏览器自动化 (备用方案)
        - 模拟真实用户行为
        - 需要处理反爬虫检测
        - 速度较慢但更灵活
        """
        logger.info("使用Selenium浏览器自动化获取评论...")
        
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36')
        
        driver = webdriver.Chrome(options=chrome_options)
        reviews = []
        
        try:
            driver.get(product_url)
            time.sleep(random.uniform(2, 4))
            
            # 点击查看所有评论
            try:
                all_reviews_link = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.PARTIAL_LINK_TEXT, "See all reviews"))
                )
                all_reviews_link.click()
                time.sleep(random.uniform(2, 4))
            except:
                logger.warning("未找到'查看所有评论'链接")
            
            page_count = 0
            while page_count < 5:  # 限制页数
                # 获取当前页面的评论
                review_elements = driver.find_elements(By.CSS_SELECTOR, '[data-hook="review"]')
                
                for element in review_elements:
                    try:
                        # 提取评论信息
                        reviewer_name = element.find_element(By.CSS_SELECTOR, '[class*="author"]').text
                        rating_element = element.find_element(By.CSS_SELECTOR, '[data-hook="review-star-rating"]')
                        rating = float(rating_element.get_attribute('class').split('a-star-')[1].split(' ')[0]) if rating_element else 0
                        
                        title_element = element.find_element(By.CSS_SELECTOR, '[data-hook="review-title"]')
                        title = title_element.text
                        
                        content_element = element.find_element(By.CSS_SELECTOR, '[data-hook="review-body"]')
                        content = content_element.text
                        
                        date_element = element.find_element(By.CSS_SELECTOR, '[data-hook="review-date"]')
                        date = date_element.text
                        
                        # 检查是否为验证购买
                        verified = len(element.find_elements(By.CSS_SELECTOR, '[data-hook="avp-badge"]')) > 0
                        
                        review = Review(
                            reviewer_name=reviewer_name,
                            rating=rating,
                            title=title,
                            content=content,
                            date=date,
                            verified_purchase=verified
                        )
                        reviews.append(review)
                        
                    except Exception as e:
                        logger.warning(f"解析评论失败: {e}")
                        continue
                
                # 尝试点击下一页
                try:
                    next_button = driver.find_element(By.CSS_SELECTOR, 'li.a-last a')
                    if next_button.get_attribute('aria-disabled') == 'true':
                        break
                    next_button.click()
                    time.sleep(random.uniform(3, 6))
                    page_count += 1
                except:
                    break
                    
        except Exception as e:
            logger.error(f"Selenium获取评论失败: {e}")
        finally:
            driver.quit()
            
        return reviews
    
    def _parse_html_reviews(self, html_content: str) -> List[Review]:
        """解析HTML内容提取评论（需要根据实际HTML结构实现）"""
        # 这里需要使用BeautifulSoup等库解析HTML
        # 具体实现略
        return []
    
    def save_reviews_to_csv(self, reviews: List[Review], filename: str):
        """保存评论到CSV文件"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['reviewer_name', 'rating', 'title', 'content', 'date', 'verified_purchase', 'helpful_votes']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for review in reviews:
                writer.writerow({
                    'reviewer_name': review.reviewer_name,
                    'rating': review.rating,
                    'title': review.title,
                    'content': review.content,
                    'date': review.date,
                    'verified_purchase': review.verified_purchase,
                    'helpful_votes': review.helpful_votes
                })
        
        logger.info(f"评论已保存到 {filename}")
    
    def save_reviews_to_json(self, reviews: List[Review], filename: str):
        """保存评论到JSON文件"""
        reviews_data = []
        for review in reviews:
            reviews_data.append({
                'reviewer_name': review.reviewer_name,
                'rating': review.rating,
                'title': review.title,
                'content': review.content,
                'date': review.date,
                'verified_purchase': review.verified_purchase,
                'helpful_votes': review.helpful_votes
            })
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(reviews_data, jsonfile, ensure_ascii=False, indent=2)
        
        logger.info(f"评论已保存到 {filename}")

def main():
    """主函数示例"""
    scraper = AmazonReviewScraper()
    
    # 从URL提取ASIN
    product_url = "https://www.amazon.nl/-/en/product-reviews/B0DG5QLWP6/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&filterByStar=all_stars&reviewerType=all_reviews&pageNumber=1#reviews-filter-bar"
    asin = "B0DG5QLWP6"  # 从URL中提取的ASIN
    
    print("Amazon评论获取工具")
    print("=" * 50)
    print("1. Rainforest API (推荐，需要API密钥)")
    print("2. ScrapFly API (需要API密钥)")
    print("3. Selenium浏览器自动化 (免费但可能不稳定)")
    
    choice = input("请选择方案 (1-3): ")
    
    if choice == "1":
        api_key = input("请输入Rainforest API密钥: ")
        reviews = scraper.method_1_rainforest_api(asin, api_key)
    elif choice == "2":
        api_key = input("请输入ScrapFly API密钥: ")
        reviews = scraper.method_2_scrapfly_api(asin, api_key)
    elif choice == "3":
        reviews = scraper.method_3_selenium_browser(product_url)
    else:
        print("无效选择")
        return
    
    if reviews:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"amazon_reviews_{asin}_{timestamp}.csv"
        json_filename = f"amazon_reviews_{asin}_{timestamp}.json"
        
        scraper.save_reviews_to_csv(reviews, csv_filename)
        scraper.save_reviews_to_json(reviews, json_filename)
        
        print(f"\n成功获取 {len(reviews)} 条评论")
        print(f"CSV文件: {csv_filename}")
        print(f"JSON文件: {json_filename}")
    else:
        print("未获取到评论数据")

if __name__ == "__main__":
    main()
